<div class="content-sidebar content-sidebar-md" data-scrollbar-target="#psScrollbarInit">
    <div class="content-sidebar-header bg-white sticky-top hstack justify-content-between">
        <h4 class="fw-bolder mb-0">Settings</h4>
    </div>
    <div class="content-sidebar-body">
        <ul class="nav flex-column nxl-content-sidebar-item">
            <?php
                $menus = [
                    [
                        'icon' => 'feather-settings',
                        'name' => 'Default',
                        'url' => route('pengaturan.default.index'),
                        'permission' => 'Pengaturan.Default',
                        'active' => ['pengaturan.default.index'],
                        'child' => [],
                    ],
                    [
                        'icon' => 'feather-mail',
                        'name' => 'eSign Mekari',
                        'permission' => 'Pengaturan.eSign Mekari',
                        'url' => route('pengaturan.e-sign.index'),
                        'active' => ['pengaturan.e-sign.index'],
                        'child' => [],
                    ],
                ];
            ?>
            <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(isset($item['permission']) && canPermission($item['permission'])): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(set_active($item['active'])); ?>" href="<?php echo e($item['url']); ?>">
                        <i class="<?php echo e($item['icon']); ?>"></i>
                        <span><?php echo e($item['name']); ?></span>
                    </a>
                </li>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
</div>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/pengaturan/sidebar.blade.php ENDPATH**/ ?>