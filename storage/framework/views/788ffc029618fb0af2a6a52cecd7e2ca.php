<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <?php echo e(isset($data) ? 'Edit' : 'Tambah'); ?> Pengaturan Jam Kerja
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <a href="<?php echo e(route('working-hour-setting.index')); ?>" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="<?php echo e(route('working-hour-setting.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php if(isset($data)): ?>
                            <input type="hidden" name="id" value="<?php echo e($data->id); ?>">
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select select2 <?php $__errorArgs = ['role_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="role_id" name="role_id" required>
                                        <option value="">Pilih Role</option>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($role->id); ?>" <?php echo e(old('role_id', $data->role_id ?? '') == $role->id ? 'selected' : ''); ?>>
                                                <?php echo e($role->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['role_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="working_hour_type" class="form-label">Jenis Jam Kerja <span class="text-danger">*</span></label>
                                    <select class="form-select select2 <?php $__errorArgs = ['working_hour_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="working_hour_type" name="working_hour_type" required>
                                        <option value="">Pilih Jenis Jam Kerja</option>
                                        <?php $__currentLoopData = $workingHourTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('working_hour_type', $data->working_hour_type ?? '') == $key ? 'selected' : ''); ?>>
                                                <?php echo e($value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['working_hour_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">Jam Mulai</label>
                                    <input type="time" class="form-control <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="start_time" name="start_time" 
                                           value="<?php echo e(old('start_time', isset($data) && $data->start_time ? date('H:i', strtotime($data->start_time)) : '')); ?>">
                                    <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">Jam Selesai</label>
                                    <input type="time" class="form-control <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="end_time" name="end_time" 
                                           value="<?php echo e(old('end_time', isset($data) && $data->end_time ? date('H:i', strtotime($data->end_time)) : '')); ?>">
                                    <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input <?php $__errorArgs = ['is_need_setting_day'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               type="checkbox" id="is_need_setting_day" name="is_need_setting_day" value="1"
                                               <?php echo e(old('is_need_setting_day', $data->is_need_setting_day ?? false) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_need_setting_day">
                                            Perlu Pengaturan Hari Kerja Khusus
                                        </label>
                                        <?php $__errorArgs = ['is_need_setting_day'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-text">
                                        Centang jika role ini memerlukan pengaturan hari kerja yang berbeda dari standar
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('working-hour-setting.index')); ?>" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                <?php echo e(isset($data) ? 'Update' : 'Simpan'); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Role:</strong> Pilih role yang akan diatur jam kerjanya</li>
                            <li><strong>Jenis Jam Kerja:</strong> Pilih tipe shift atau jam kerja</li>
                            <li><strong>Jam Mulai/Selesai:</strong> Tentukan waktu mulai dan selesai kerja</li>
                            <li><strong>Pengaturan Hari:</strong> Centang jika perlu atur hari kerja khusus</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Catatan:</h6>
                        <ul class="mb-0">
                            <li>Setiap role hanya bisa memiliki satu pengaturan jam kerja</li>
                            <li>Jam selesai harus lebih besar dari jam mulai</li>
                            <li>Untuk shift malam, gunakan format 24 jam</li>
                        </ul>
                    </div>
                    
                    <?php if(isset($data)): ?>
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> <?php echo e($data->created_at->translatedFormat('d F Y H:i')); ?></li>
                            <?php if($data->updated_at != $data->created_at): ?>
                            <li><strong>Diupdate:</strong> <?php echo e($data->updated_at->translatedFormat('d F Y H:i')); ?></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            $(document).ready(function() {
                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });
                    
                    // Validate time range
                    let startTime = $('#start_time').val();
                    let endTime = $('#end_time').val();
                    
                    if (startTime && endTime && startTime >= endTime) {
                        isValid = false;
                        $('#end_time').addClass('is-invalid');
                        toastr.error('Jam selesai harus lebih besar dari jam mulai');
                    }
                    
                    if (!isValid) {
                        e.preventDefault();
                        toastr.error('Mohon lengkapi semua field yang wajib diisi');
                    }
                });

                // Clear validation on input change
                $('input, select').on('change', function() {
                    $(this).removeClass('is-invalid');
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/working-hour-setting/create-update.blade.php ENDPATH**/ ?>