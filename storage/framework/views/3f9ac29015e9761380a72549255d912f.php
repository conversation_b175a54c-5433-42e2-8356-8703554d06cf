<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Master Data Kalender Kerja
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 

     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 200px">
                            <select class="form-select select2" id="yearFilter">
                                <?php $__currentLoopData = $years; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($year); ?>" <?php echo e($year == $currentYear ? 'selected' : ''); ?>>
                                        Tahun <?php echo e($year); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <select class="form-select select2" id="weekFilter">
                                <option value="">Semua Minggu</option>
                            </select>
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-info" data-bs-toggle="modal"
                                    data-bs-target="#uploadModal">
                                    <i class="feather-upload me-2"></i>
                                    <span>Upload Calendar</span>
                                </button>
                                <button type="button" class="btn btn-success" id="generateCalendar">
                                    <i class="feather-calendar me-2"></i>
                                    <span>Generate Kalender</span>
                                </button>
                            </div>
                            
                        </div>
                    </div>

                    <!-- Today's Information -->
                    <div class="alert alert-success mt-3" id="todayInfo" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="feather-calendar me-2"></i>
                            <div>
                                <strong>Hari Ini:</strong> <span id="todayDate"></span><br>
                                <small><strong>Minggu ke-</strong><span id="todayWeek"></span> <strong>Tahun</strong> <span id="todayYear"></span></small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Kalender Kerja:</h6>
                        <ul class="mb-0">
                            <li><strong>Upload Calendar:</strong> Upload file Excel dengan format kalender</li>
                            <li><strong>Generate Kalender:</strong> Membuat data kalender untuk satu tahun penuh</li>
                            <li><strong>Holiday:</strong> Hari libur nasional atau khusus</li>
                        </ul>
                    </div>

                    <!-- Calendar View -->
                    <div class="mt-4" id="calendarView">
                        <!-- Calendar Navigation -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary" id="prevWeek">
                                    <i class="feather-chevron-left"></i> Minggu Sebelumnya
                                </button>
                                <button type="button" class="btn btn-outline-success" id="goToToday">
                                    <i class="feather-calendar"></i> Hari Ini
                                </button>
                            </div>
                            <h5 class="mb-0" id="currentWeekTitle">Minggu 1 - 2024</h5>
                            <button type="button" class="btn btn-outline-primary" id="nextWeek">
                                Minggu Selanjutnya <i class="feather-chevron-right"></i>
                            </button>
                        </div>

                        <!-- Calendar Container with horizontal scroll -->
                        <div class="calendar-container">
                            <div id="weeklyCalendar">
                                <!-- Calendar akan di-generate di sini -->
                            </div>
                        </div>
                    </div>

                    <!-- Table View (Hidden by default) -->
                    <div class="table-responsive mt-4" id="tableView" style="display: none;">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Tanggal</th>
                                    <th>Hari</th>
                                    <th>Minggu Ke</th>
                                    <th>Jenis Hari</th>
                                    <th>Libur</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>

                    <!-- View Toggle -->
                    <div class="mt-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" id="calendarViewBtn">
                                <i class="feather-calendar me-2"></i>Tampilan Kalender
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="tableViewBtn">
                                <i class="feather-list me-2"></i>Tampilan Tabel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <?php $__env->startPush('modals'); ?>
        <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="uploadModalLabel">Upload Calendar Excel</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="calendar_file" class="form-label">File Excel <span
                                        class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="calendar_file" name="calendar_file"
                                    accept=".xlsx,.xls" required>
                                <div class="form-text">Format: .xlsx atau .xls</div>
                            </div>

                            <div class="alert alert-warning">
                                <h6 class="alert-heading">Format Excel yang Diharapkan:</h6>
                                <ul class="mb-0">
                                    <li><strong>Kolom A:</strong> Tanggal (format: YYYY-MM-DD, contoh: 2024-01-01)</li>
                                    <li><strong>Kolom B:</strong> Jenis_Hari (weekdays/weekend/holiday)</li>
                                </ul>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="feather-info me-1"></i>
                                        Nama hari dan minggu ke akan otomatis dihitung oleh sistem berdasarkan tanggal.
                                    </small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <a href="<?php echo e(route('working-calendar-setting.downloadTemplate')); ?>"
                                    class="btn btn-outline-success btn-sm" target="_blank">
                                    <i class="feather-download me-2"></i>Download Template Excel
                                </a>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="feather-x me-2"></i>Batal
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-upload me-2"></i>Upload
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php $__env->stopPush(); ?>

    <?php echo $__env->make('components.alert-confirmation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php $__env->startPush('styles'); ?>
        <style>
            .day-type-select {
                border: 1px solid #dee2e6;
                transition: all 0.2s ease;
            }

            .day-type-select:focus {
                border-color: #0d6efd;
                box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            }

            .day-type-select.changed {
                border-color: #ffc107;
                background-color: #fff3cd;
            }

            .border-warning {
                border-color: #ffc107 !important;
            }

            .border-2 {
                border-width: 2px !important;
            }

            #saveWeekChanges:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            .edit-type-btn {
                opacity: 0.7;
                transition: opacity 0.2s ease;
            }

            .edit-type-btn:hover {
                opacity: 1;
            }

            .type-display {
                min-width: 60px;
                text-align: center;
            }

            .d-flex.align-items-center.justify-content-center {
                min-height: 30px;
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <?php echo $__env->make('libs.datatable', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <script>
            let table;
            let currentView = 'calendar';
            let currentWeek = 1;
            let currentYear = new Date().getFullYear();

            // Function to get current week number
            function getCurrentWeekNumber() {
                const today = new Date();
                const startOfYear = new Date(today.getFullYear(), 0, 1);
                const pastDaysOfYear = (today - startOfYear) / 86400000;
                return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
            }

            // Function to get number of weeks in a year
            function getWeeksInYear(year) {
                const lastDayOfYear = new Date(year, 11, 31);
                const startOfYear = new Date(year, 0, 1);
                const pastDaysOfYear = (lastDayOfYear - startOfYear) / 86400000;
                return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
            }

            // Function to get today's information
            function getTodayInfo() {
                const today = new Date();
                const weekNumber = getCurrentWeekNumber();
                const dateString = today.toLocaleDateString('id-ID', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                return {
                    date: today,
                    weekNumber: weekNumber,
                    dateString: dateString,
                    year: today.getFullYear()
                };
            }

            $(document).ready(function() {
                // Get today's information and set current week
                const todayInfo = getTodayInfo();
                currentWeek = todayInfo.weekNumber;
                currentYear = todayInfo.year;

                // Show today's information in UI
                $('#todayDate').text(todayInfo.dateString);
                $('#todayWeek').text(todayInfo.weekNumber);
                $('#todayYear').text(todayInfo.year);
                $('#todayInfo').show();

                // Show today's information in console
                console.log(`Hari ini: ${todayInfo.dateString}`);
                console.log(`Minggu ke-${todayInfo.weekNumber} Tahun ${todayInfo.year}`);

                // Set year filter to current year
                $('#yearFilter').val(currentYear);

                // Initialize calendar view
                loadCalendarView();
                loadWeekOptions();

                // Navigation handlers
                $('#prevWeek').on('click', function() {
                    if (currentWeek > 1) {
                        // Go to previous week in same year
                        currentWeek--;
                    } else {
                        // Go to last week of previous year
                        currentYear--;
                        currentWeek = 52; // Most years have 52 weeks, some have 53

                        // Update year filter
                        $('#yearFilter').val(currentYear);
                        loadWeekOptions();
                    }
                    updateCalendarView();
                });

                $('#nextWeek').on('click', function() {
                    // Get the actual number of weeks in current year
                    const weeksInYear = getWeeksInYear(currentYear);

                    if (currentWeek < weeksInYear) {
                        // Go to next week in same year
                        currentWeek++;
                    } else {
                        // Go to first week of next year
                        currentYear++;
                        currentWeek = 1;

                        // Update year filter
                        $('#yearFilter').val(currentYear);
                        loadWeekOptions();
                    }
                    updateCalendarView();
                });

                // Go to today handler
                $('#goToToday').on('click', function() {
                    const todayInfo = getTodayInfo();
                    currentWeek = todayInfo.weekNumber;
                    currentYear = todayInfo.year;

                    // Update year filter
                    $('#yearFilter').val(currentYear);

                    // Update week filter
                    loadWeekOptions();

                    // Update calendar view
                    updateCalendarView();

                    // Show notification
                    Swal.fire({
                        icon: 'info',
                        title: 'Navigasi ke Hari Ini',
                        text: `Menampilkan Minggu ke-${currentWeek} Tahun ${currentYear}`,
                        timer: 2000,
                        showConfirmButton: false
                    });
                });
                $('#goToToday').on('click', function() {
                    const todayInfo = getTodayInfo();
                    currentWeek = todayInfo.weekNumber;
                    currentYear = todayInfo.year;

                    // Update year filter
                    $('#yearFilter').val(currentYear);

                    // Update week filter
                    loadWeekOptions();

                    // Update calendar view
                    updateCalendarView();

                    // Show notification
                    Swal.fire({
                        icon: 'info',
                        title: 'Navigasi ke Hari Ini',
                        text: `Menampilkan Minggu ke-${currentWeek} Tahun ${currentYear}`,
                        timer: 2000,
                        showConfirmButton: false
                    });
                });

                // Initialize DataTable (hidden by default)
                table = $('#example').DataTable({
                    processing: true,
                    serverSide: true,
                    searching: false,
                    lengthChange: false,
                    ajax: {
                        url: "<?php echo e(route('working-calendar-setting.dataTable')); ?>",
                        type: "POST",
                        data: function(d) {
                            d._token = "<?php echo e(csrf_token()); ?>";
                            d.year = $('#yearFilter').val();
                            d.week = $('#weekFilter').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'date_formatted',
                            name: 'date'
                        },
                        {
                            data: 'day_name',
                            name: 'day_name'
                        },
                        {
                            data: 'week_number',
                            name: 'week_number'
                        },
                        {
                            data: 'day_type_badge',
                            name: 'day_type',
                            orderable: false
                        },
                        {
                            data: 'is_holiday_badge',
                            name: 'is_holiday',
                            orderable: false
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                    order: [
                        [1, 'desc']
                    ],
                    language: {
                        processing: "Memproses...",
                        zeroRecords: "Data tidak ditemukan",
                        info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                        infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                        infoFiltered: "(difilter dari _MAX_ total data)",
                        paginate: {
                            first: "Pertama",
                            last: "Terakhir",
                            next: "Selanjutnya",
                            previous: "Sebelumnya"
                        }
                    }
                });

                // View toggle handlers
                $('#calendarViewBtn').on('click', function() {
                    currentView = 'calendar';
                    $('#calendarView').show();
                    $('#tableView').hide();
                    $(this).addClass('active');
                    $('#tableViewBtn').removeClass('active');
                    loadCalendarView();
                });

                $('#tableViewBtn').on('click', function() {
                    currentView = 'table';
                    $('#calendarView').hide();
                    $('#tableView').show();
                    $(this).addClass('active');
                    $('#calendarViewBtn').removeClass('active');
                    table.ajax.reload();
                });

                // Filter change handlers
                $('#yearFilter').on('change', function() {
                    currentYear = $(this).val();
                    currentWeek = 1; // Reset to week 1 when year changes
                    loadWeekOptions();
                    if (currentView === 'calendar') {
                        updateCalendarView();
                    } else {
                        table.ajax.reload();
                    }
                });

                $('#weekFilter').on('change', function() {
                    currentWeek = parseInt($(this).val()) || 1;
                    if (currentView === 'calendar') {
                        updateCalendarView();
                    } else {
                        table.ajax.reload();
                    }
                });

                // Generate calendar functionality
                $('#generateCalendar').on('click', function() {
                    let year = $('#yearFilter').val();
                    let button = $(this);

                    Swal.fire({
                        title: 'Konfirmasi Generate Kalender',
                        text: `Anda akan membuat kalender untuk tahun ${year}. Data yang sudah ada tidak akan ditimpa.`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Ya, Generate',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            button.prop('disabled', true).html(
                                '<i class="feather-loader me-2"></i>Generating...');
                            $.ajax({
                                url: "<?php echo e(route('working-calendar-setting.generateCalendar')); ?>",
                                type: 'POST',
                                data: {
                                    _token: "<?php echo e(csrf_token()); ?>",
                                    year: year
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Berhasil!',
                                            text: response.message,
                                            timer: 2000,
                                            showConfirmButton: false
                                        });
                                        table.ajax.reload();
                                        generateCalendar(year, button);
                                    } else {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Gagal!',
                                            text: response.message
                                        });
                                    }
                                },
                                error: function(xhr) {
                                    let response = xhr.responseJSON;
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Terjadi Kesalahan!',
                                        text: response.message ||
                                            'Terjadi kesalahan'
                                    });
                                },
                                complete: function() {
                                    button.prop('disabled', false).html(
                                        '<i class="feather-calendar me-2"></i>Generate Kalender'
                                    );
                                }
                            });
                        }
                    });
                });

                // Delete functionality
                $(document).on('click', '.deleteData', function() {
                    let id = $(this).data('id');
                    let input = $(this).data('input');

                    $('#confirmationModal').modal('show');
                    $('#confirmationModal .modal-body p').text(
                        `Apakah Anda yakin ingin menghapus data tanggal "${input.date_formatted}"?`);

                    $('#confirmDelete').off('click').on('click', function() {
                        $.ajax({
                            url: "<?php echo e(url('working-calendar-setting')); ?>/" + id,
                            type: 'DELETE',
                            data: {
                                _token: "<?php echo e(csrf_token()); ?>"
                            },
                            success: function(response) {
                                $('#confirmationModal').modal('hide');
                                if (response.status) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Berhasil!',
                                        text: response.message,
                                        timer: 2000,
                                        showConfirmButton: false
                                    });
                                    table.ajax.reload();
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Gagal!',
                                        text: response.message
                                    });
                                }
                            },
                            error: function(xhr) {
                                $('#confirmationModal').modal('hide');
                                let response = xhr.responseJSON;
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Terjadi Kesalahan!',
                                    text: response.message || 'Terjadi kesalahan'
                                });
                            }
                        });
                    });
                });

                // Upload form handler
                $('#uploadForm').on('submit', function(e) {
                    e.preventDefault();

                    let formData = new FormData(this);
                    formData.append('_token', "<?php echo e(csrf_token()); ?>");

                    $.ajax({
                        url: "<?php echo e(route('working-calendar-setting.uploadExcel')); ?>",
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function() {
                            $('#uploadModal .btn-primary').prop('disabled', true).html(
                                '<i class="feather-loader me-2"></i>Uploading...');
                        },
                        success: function(response) {
                            if (response.status) {
                                $('#uploadModal').modal('hide');
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Upload Berhasil!',
                                    text: response.message,
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                loadCalendarView();
                                $('#uploadForm')[0].reset();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Gagal!',
                                    text: response.message
                                });
                            }
                        },
                        error: function(xhr) {
                            let response = xhr.responseJSON;
                            Swal.fire({
                                icon: 'error',
                                title: 'Terjadi Kesalahan!',
                                text: response.message || 'Terjadi kesalahan saat upload'
                            });
                        },
                        complete: function() {
                            $('#uploadModal .btn-primary').prop('disabled', false).html(
                                '<i class="feather-upload me-2"></i>Upload');
                        }
                    });
                });
            });

            function loadWeekOptions() {
                let year = $('#yearFilter').val();
                let weekSelect = $('#weekFilter');

                weekSelect.html('<option value="">Semua Minggu</option>');

                // Generate week options (1-53)
                for (let i = 1; i <= 53; i++) {
                    const selected = (i === currentWeek) ? 'selected' : '';
                    weekSelect.append(`<option value="${i}" ${selected}>Minggu ${i}</option>`);
                }

                // Trigger Select2 update if it's initialized
                if (weekSelect.hasClass('select2-hidden-accessible')) {
                    weekSelect.trigger('change.select2');
                }
            }

            function loadCalendarView() {
                currentYear = $('#yearFilter').val();
                updateCalendarView();
            }

            function updateCalendarView() {
                // Update week title with basic info (will be enhanced when data loads)
                $('#currentWeekTitle').text(`Minggu ke-${currentWeek} Tahun ${currentYear}`);

                $.ajax({
                    url: "<?php echo e(route('working-calendar-setting.getCalendarData')); ?>",
                    type: 'GET',
                    data: {
                        year: currentYear,
                        week: currentWeek
                    },
                    beforeSend: function() {
                        $('#weeklyCalendar').html(
                            '<div class="text-center p-4"><i class="feather-loader"></i> Memuat kalender...</div>'
                        );
                    },
                    success: function(response) {
                        renderCalendar(response);
                    },
                    error: function(xhr) {
                        let errorMessage = 'Gagal memuat data kalender';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage += ': ' + xhr.responseJSON.message;
                        }

                        $('#weeklyCalendar').html(`
                            <div class="alert alert-danger">${errorMessage}</div>
                            <div class="text-center">
                                <button type="button" class="btn btn-success" id="generateCalendarBtn">
                                    <i class="feather-calendar me-2"></i>Generate Kalender
                                </button>
                            </div>
                        `);

                        $('#generateCalendarBtn').on('click', function() {
                            generateCalendar();
                        });

                        console.error('Error loading calendar:', xhr);
                    }
                });
            }

            function generateCalendar() {
                let year = $('#yearFilter').val();

                $.ajax({
                    url: "<?php echo e(route('working-calendar-setting.generateCalendar')); ?>",
                    type: 'POST',
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        year: year
                    },
                    beforeSend: function() {
                        $('#weeklyCalendar').html(
                            '<div class="text-center p-4"><i class="feather-loader"></i> Generating calendar...</div>'
                        );
                    },
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Generate Berhasil!',
                                text: response.message,
                                timer: 2000,
                                showConfirmButton: false
                            });
                            loadCalendarView();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Generate Gagal!',
                                text: response.message
                            });
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Gagal generate kalender';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage += ': ' + xhr.responseJSON.message;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: 'Terjadi Kesalahan!',
                            text: errorMessage
                        });
                    }
                });
            }

            function renderCalendar(data) {
                if (!data || data.length === 0) {
                    $('#weeklyCalendar').html(
                        '<div class="alert alert-info">Tidak ada data kalender untuk periode yang dipilih</div>');
                    return;
                }

                // Sort data by date
                data.sort((a, b) => new Date(a.date) - new Date(b.date));

                // Get date range for this week
                let firstDay = new Date(data[0].date);
                let lastDay = new Date(data[data.length - 1].date);

                // Create detailed week information
                let firstDayFormatted = firstDay.toLocaleDateString('id-ID', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });

                let lastDayFormatted = lastDay.toLocaleDateString('id-ID', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });

                // Short format for card subtitle
                let weekDateRange =
                    `${firstDay.getDate()} ${firstDay.toLocaleDateString('id-ID', {month: 'short'})} - ${lastDay.getDate()} ${lastDay.toLocaleDateString('id-ID', {month: 'short'})} ${lastDay.getFullYear()}`;

                // Detailed format for main title
                let detailedWeekTitle = `Minggu ke-${currentWeek} Bulan ${firstDay.toLocaleDateString('id-ID', {month: 'long'})} ${firstDayFormatted} - ${lastDayFormatted}`;

                // Update week title with detailed information
                $('#currentWeekTitle').text(detailedWeekTitle);

                let calendarHtml = `
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-0">Minggu ke-${currentWeek} Bulan ${firstDay.toLocaleDateString('id-ID', {month: 'long'})} ${currentYear}</h6>
                                <small class="text-muted">${weekDateRange}</small>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <div>
                                    <span class="badge bg-primary me-1">Weekdays</span>
                                    <span class="badge bg-warning me-1">Weekend</span>
                                    <span class="badge bg-danger">Holiday</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <button type="button" id="saveWeekChanges" class="btn btn-success" disabled>
                                    <i class="feather-save pe-1"></i> Simpan Perubahan
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="overflow-x: auto; white-space: nowrap; max-width: 1200px">
                            <div class="table-responsive" style="overflow-x: auto;">
                                <table class="table table-bordered mb-0" style="min-width: 1000px;">
                                    <tbody>
                `;

                // Row 1: Weeknum
                calendarHtml += '<tr>';
                calendarHtml +=
                    '<th class="text-center align-middle bg-secondary text-white" style="width: 100px; height: 50px; position: sticky; top: 0">Weeknum</th>';
                data.forEach(function(day) {
                    calendarHtml +=
                        `<td class="text-center align-middle fw-bold bg-light" style="width: 120px; height: 50px; position: sticky; top: 0">${day.week_number}</td>`;
                });
                calendarHtml += '</tr>';

                // Row 2: Tanggal
                calendarHtml += '<tr>';
                calendarHtml +=
                    '<th class="text-center align-middle bg-secondary text-white" style="width: 100px; height: 50px; position: sticky; top: 0">Tanggal</th>';
                data.forEach(function(day) {
                    let date = new Date(day.date);
                    // Format tanggal lengkap: DD/MM/YYYY
                    let formattedDate = date.toLocaleDateString('id-ID', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                    });

                    let cellClass = 'bg-light';
                    switch (day.day_type) {
                        case 'weekend':
                            cellClass = 'bg-warning text-dark';
                            break;
                        case 'weekdays':
                            cellClass = 'bg-primary text-white';
                            break;
                        case 'holiday':
                            cellClass = 'bg-danger text-white';
                            break;
                    }

                    calendarHtml +=
                        `<td class="text-center align-middle ${cellClass}" style="width: 120px; height: 50px; position: sticky; top: 0 font-size: 12px;">${formattedDate}</td>`;
                });
                calendarHtml += '</tr>';

                // Row 3: Type
                calendarHtml += '<tr>';
                calendarHtml +=
                    '<th class="text-center align-middle bg-secondary text-white" style="width: 100px; height: 50px; position: sticky; top: 0">Jenis Hari</th>';
                data.forEach(function(day) {
                    let cellClass = 'bg-light';
                    let currentType = '';
                    let typeDisplayText = '';

                    // Determine current type
                    switch (day.day_type) {
                        case 'weekend':
                            currentType = 'weekend';
                            typeDisplayText = 'Weekend';
                            cellClass = 'bg-warning-subtle';
                            break;
                        case 'weekdays':
                            currentType = 'weekdays';
                            typeDisplayText = 'Weekdays';
                            cellClass = 'bg-primary-subtle';
                            break;
                        case 'holiday':
                            currentType = 'holiday';
                            typeDisplayText = 'Holiday';
                            cellClass = 'bg-danger-subtle';
                            break;
                        default:
                            currentType = '';
                            typeDisplayText = '';
                            cellClass = 'bg-light';
                            break;
                    }

                    let cellContent = '';

                    if (currentType === '' || currentType === null) {
                        // Show select dropdown directly for empty values
                        cellContent = `
                            <select class="form-select form-select-sm day-type-select"
                                    data-date="${day.date}"
                                    data-day-id="${day.id}"
                                    data-original-value="${currentType}"
                                    style="font-size: 11px;">
                                <option value="">Pilih Jenis</option>
                                <option value="weekdays">Weekdays</option>
                                <option value="weekend">Weekend</option>
                                <option value="holiday">Holiday</option>
                            </select>
                        `;
                    } else {
                        // Show type text with edit icon for existing values
                        cellContent = `
                            <div class="d-flex align-items-center justify-content-center gap-1">
                                <span class="type-display" data-date="${day.date}" style="font-size: 14px; font-weight: bold;">${typeDisplayText}</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary edit-type-btn"
                                        data-date="${day.date}"
                                        data-day-id="${day.id}"
                                        data-original-value="${currentType}"
                                        style="padding: 2px 4px; font-size: 10px; line-height: 1;">
                                    <i class="feather-edit" style="width: 10px; height: 10px;"></i>
                                </button>
                            </div>
                            <select class="form-select form-select-sm day-type-select d-none"
                                    data-date="${day.date}"
                                    data-day-id="${day.id}"
                                    data-original-value="${currentType}"
                                    style="font-size: 11px;">
                                <option value="">Pilih Jenis</option>
                                <option value="weekdays" ${currentType === 'weekdays' ? 'selected' : ''}>Weekdays</option>
                                <option value="weekend" ${currentType === 'weekend' ? 'selected' : ''}>Weekend</option>
                                <option value="holiday" ${currentType === 'holiday' ? 'selected' : ''}>Holiday</option>
                            </select>
                        `;
                    }

                    calendarHtml +=
                        `<td class="text-center align-middle ${cellClass}" style="width: 120px; height: 50px; padding: 5px;">${cellContent}</td>`;
                });
                calendarHtml += '</tr>';

                // Row 4: Hari
                calendarHtml += '<tr>';
                calendarHtml +=
                    '<th class="text-center align-middle bg-secondary text-white" style="width: 100px; height: 50px;">Hari</th>';
                data.forEach(function(day) {
                    let dayName = day.day_name.substring(0, 3); // Singkat nama hari
                    let cellClass = 'bg-light';

                    switch (day.day_type) {
                        case 'weekend':
                            cellClass = 'bg-warning text-dark';
                            break;
                        case 'weekdays':
                            cellClass = 'bg-primary text-white';
                            break;
                        case 'holiday':
                            cellClass = 'bg-danger text-white';
                            break;
                        default:
                            cellClass = 'bg-light';
                            break;
                    }

                    calendarHtml +=
                        `<td class="text-center align-middle ${cellClass}" style="width: 120px; height: 50px;">${dayName}</td>`;
                });
                calendarHtml += '</tr>';

                calendarHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <?php if(canPermission('Working Calendar Setting.Create')): ?>
                                    <a href="<?php echo e(url('working-calendar-setting/create')); ?>" class="btn btn-sm btn-primary">
                                        <i class="feather-plus"></i> Tambah Data
                                    </a>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if(canPermission('Working Calendar Setting.Import')): ?>
                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#importModal">
                                        <i class="feather-upload"></i> Import Excel
                                    </button>
                                    <?php endif; ?>

                                    <?php if(canPermission('Working Calendar Setting.Export')): ?>
                                    <a href="<?php echo e(url('working-calendar-setting/export-template')); ?>" class="btn btn-sm btn-info">
                                        <i class="feather-download"></i> Download Template
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#weeklyCalendar').html(calendarHtml);

                // Destroy any existing Select2 instances in the calendar area
                $('#weeklyCalendar .day-type-select').each(function() {
                    if ($(this).hasClass('select2-hidden-accessible')) {
                        $(this).select2('destroy');
                    }
                });

                // Enable/disable navigation buttons
                $('#prevWeek').prop('disabled', currentWeek <= 1);
                $('#nextWeek').prop('disabled', currentWeek >= 53);

                // Initialize change tracking
                initializeChangeTracking();
            }

            // Track changes in day type selects
            function initializeChangeTracking() {
                // Remove any existing Select2 from day-type-select elements
                $('.day-type-select').each(function() {
                    const $select = $(this);

                    // Destroy Select2 if it exists
                    if ($select.hasClass('select2-hidden-accessible')) {
                        $select.select2('destroy');
                    }

                    // Remove select2 class to prevent auto-initialization
                    $select.removeClass('select2');

                    // Store original values and ensure proper initialization
                    const originalValue = $select.data('original-value') || $select.val() || '';
                    $select.data('original-value', originalValue);

                    console.log('Initialized select:', {
                        date: $select.data('date'),
                        originalValue: originalValue,
                        currentValue: $select.val()
                    });
                });

                // Handle edit button clicks
                $(document).off('click', '.edit-type-btn').on('click', '.edit-type-btn', function() {
                    const $btn = $(this);
                    const $cell = $btn.closest('td');
                    const $display = $cell.find('.type-display');
                    const $select = $cell.find('.day-type-select');

                    // Hide display and button, show select
                    $btn.closest('.d-flex').addClass('d-none');
                    $select.removeClass('d-none').focus();
                });

                // Handle select change events
                $(document).off('change', '.day-type-select').on('change', '.day-type-select', function() {
                    const $select = $(this);
                    const $cell = $select.closest('td');
                    const originalValue = $select.data('original-value') || '';
                    const currentValue = $select.val() || '';

                    console.log('Select changed:', {
                        originalValue: originalValue,
                        currentValue: currentValue,
                        date: $select.data('date')
                    });

                    // Mark as changed if different from original
                    if (originalValue !== currentValue) {
                        $select.addClass('changed');
                        $cell.addClass('border-warning border-2');
                    } else {
                        $select.removeClass('changed');
                        $cell.removeClass('border-warning border-2');
                    }

                    // Update the display text and show it back
                    if (currentValue !== '') {
                        const displayText = $select.find('option:selected').text();
                        const $display = $cell.find('.type-display');
                        const $displayContainer = $cell.find('.d-flex');

                        // Update display text
                        if ($display.length > 0) {
                            $display.text(displayText);
                            $select.addClass('d-none');
                            $displayContainer.removeClass('d-none');
                        }
                    }

                    // Update save button visibility
                    updateSaveButtonState();
                });

                // Handle clicking outside select to cancel edit
                $(document).off('blur', '.day-type-select').on('blur', '.day-type-select', function(e) {
                    const $select = $(this);
                    const $cell = $select.closest('td');
                    const currentValue = $select.val();

                    // Add a small delay to allow for other click events to process first
                    setTimeout(function() {
                        // Only hide if there's a value and the select is not focused
                        if (currentValue !== '' && !$select.is(':focus')) {
                            const $displayContainer = $cell.find('.d-flex');
                            if ($displayContainer.length > 0) {
                                $select.addClass('d-none');
                                $displayContainer.removeClass('d-none');
                            }
                        }
                    }, 100);
                });

                // Prevent select from losing focus when clicking on other calendar elements
                $(document).off('click', '.day-type-select').on('click', '.day-type-select', function(e) {
                    e.stopPropagation();
                });

                // Handle clicks on calendar cells to properly manage select visibility
                $(document).off('click', '#weeklyCalendar td').on('click', '#weeklyCalendar td', function(e) {
                    const $cell = $(this);
                    const $clickedSelect = $(e.target).closest('.day-type-select');

                    // If clicking on a select, don't do anything
                    if ($clickedSelect.length > 0) {
                        return;
                    }

                    // Hide any visible selects in other cells
                    $('#weeklyCalendar .day-type-select').not('.d-none').each(function() {
                        const $select = $(this);
                        const $selectCell = $select.closest('td');
                        const currentValue = $select.val();

                        if (currentValue !== '' && $selectCell[0] !== $cell[0]) {
                            const $displayContainer = $selectCell.find('.d-flex');
                            if ($displayContainer.length > 0) {
                                $select.addClass('d-none');
                                $displayContainer.removeClass('d-none');
                            }
                        }
                    });
                });
            }

            // Update save button state
            function updateSaveButtonState() {
                const hasChanges = $('.day-type-select.changed').length > 0;
                $('#saveWeekChanges').prop('disabled', !hasChanges);

                if (hasChanges) {
                    $('#saveWeekChanges').removeClass('btn-success').addClass('btn-warning');
                    $('#saveWeekChanges').html('<i class="feather-save"></i> Simpan Perubahan (' + $('.day-type-select.changed')
                        .length + ')');
                } else {
                    $('#saveWeekChanges').removeClass('btn-warning').addClass('btn-success');
                    $('#saveWeekChanges').html('<i class="feather-save"></i> Simpan Perubahan');
                }
            }

            // Save week changes
            $(document).off('click', '#saveWeekChanges').on('click', '#saveWeekChanges', function() {
                const changes = [];

                $('.day-type-select.changed').each(function() {
                    const $select = $(this);
                    const date = $select.data('date');
                    const newType = $select.val();

                    changes.push({
                        date: date,
                        day_type: newType,
                        is_holiday: newType === 'holiday'
                    });
                });

                if (changes.length === 0) {
                    Swal.fire('Info', 'Tidak ada perubahan untuk disimpan', 'info');
                    return;
                }

                // Show loading
                $(this).prop('disabled', true).html('<i class="spinner-border spinner-border-sm"></i> Menyimpan...');

                // Send AJAX request
                $.ajax({
                    url: '<?php echo e(route('working-calendar-setting.bulk-update')); ?>',
                    method: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        changes: changes
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Berhasil', 'Perubahan berhasil disimpan', 'success');

                            // Update original values and remove change indicators
                            $('.day-type-select.changed').each(function() {
                                const $select = $(this);
                                const $cell = $select.closest('td');
                                const newValue = $select.val();
                                const displayText = $select.find('option:selected').text();

                                // Update original value
                                $select.data('original-value', newValue);
                                $select.removeClass('changed');
                                $cell.removeClass('border-warning border-2');

                                // Update display text
                                const $display = $cell.find('.type-display');
                                if ($display.length > 0) {
                                    $display.text(displayText);
                                }

                                // If this was an empty field that now has value, convert to display mode
                                if (newValue !== '' && !$cell.find('.edit-type-btn').length) {
                                    const $displayContainer = $(`
                                        <div class="d-flex align-items-center justify-content-center gap-1">
                                            <span class="type-display" data-date="${$select.data('date')}" style="font-size: 11px; font-weight: 500;">${displayText}</span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary edit-type-btn"
                                                    data-date="${$select.data('date')}"
                                                    data-day-id="${$select.data('day-id')}"
                                                    data-original-value="${newValue}"
                                                    style="padding: 2px 4px; font-size: 10px; line-height: 1;">
                                                <i class="feather-edit" style="width: 10px; height: 10px;"></i>
                                            </button>
                                        </div>
                                    `);

                                    $cell.prepend($displayContainer);
                                    $select.addClass('d-none');
                                }
                            });

                            // Reset save button
                            updateSaveButtonState();

                            // Re-initialize change tracking for new elements
                            initializeChangeTracking();

                            loadCalendarView();

                        } else {
                            Swal.fire('Error', response.message || 'Terjadi kesalahan', 'error');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error:', xhr);
                        Swal.fire('Error', 'Terjadi kesalahan saat menyimpan', 'error');
                    },
                    complete: function() {
                        $('#saveWeekChanges').prop('disabled', false);
                        updateSaveButtonState();
                    }
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/working-calendar-setting/index.blade.php ENDPATH**/ ?>