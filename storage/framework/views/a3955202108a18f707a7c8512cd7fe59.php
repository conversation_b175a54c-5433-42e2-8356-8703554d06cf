<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Master Data Pengaturan Jenis Absen
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 

     <?php $__env->endSlot(); ?>

        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center flex-wrap">
                            <div class="" style="width: 300px">
                                <input type="text" class="form-control" id="search-input" placeholder="Cari nama/kode jenis absen"
                                    oninput="handleSearch(event)"
                                    style="width: 100%" />
                            </div>
                            <div class="ms-md-auto mt-md-0 mt-3">
                                
                                <div>
                                    <a href="<?php echo e(route('attendance-type-setting.create')); ?>" class="btn btn-primary">
                                        <i class="feather-plus me-2"></i>
                                        <span>Tambah Jenis Absen</span>
                                    </a>
                                </div>
                                
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6 class="alert-heading">Informasi Jenis Absen:</h6>
                            <ul class="mb-0">
                                <li><strong>Pengurangan Hari Cuti:</strong> Jumlah hari yang dikurangi dari jatah cuti</li>
                                <li><strong>Pengurangan Hari Libur:</strong> Jumlah hari yang dikurangi dari jatah libur</li>
                                <li><strong>Tambahan Hari:</strong> Jumlah hari tambahan yang diberikan</li>
                                <li>Contoh: Sakit = mengurangi cuti, Alpha = mengurangi cuti dan libur</li>
                            </ul>
                        </div>
                        
                        <div class="table-responsive mt-4">
                            <table class="table table-hover" id="example">
                                <thead>
                                    <tr>
                                        <th style="width: 12px">No</th>
                                        <th>Nama</th>
                                        <th>Kode</th>
                                        <th>Pengurangan Cuti</th>
                                        <th>Pengurangan Libur</th>
                                        <th>Tambahan Hari</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php echo $__env->make('components.alert-confirmation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php $__env->startPush('scripts'); ?>
        <?php echo $__env->make('libs.datatable', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <script>
            let table;
            let searchTimeout;

            $(document).ready(function() {
                table = $('#example').DataTable({
                   lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "<?php echo e(route('attendance-type-setting.dataTable')); ?>",
                        type: "POST",
                        data: function(d) {
                            d._token = "<?php echo e(csrf_token()); ?>";
                            d.keyword = $('#search-input').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'name',
                            name: 'name'
                        },
                        {
                            data: 'code',
                            name: 'code'
                        },
                        {
                            data: 'reduction_leave_formatted',
                            name: 'reduction_days_leave'
                        },
                        {
                            data: 'reduction_off_formatted',
                            name: 'reduction_days_off'
                        },
                        {
                            data: 'additional_formatted',
                            name: 'additional_days'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                });

                // Delete functionality
                $(document).on('click', '.deleteData', function() {
                    let id = $(this).data('id');
                    let input = $(this).data('input');
                    
                    $('#confirmationModal').modal('show');
                    $('#confirmationModal .modal-body p').text(`Apakah Anda yakin ingin menghapus jenis absen "${input.name}"?`);
                    
                    $('#confirmDelete').off('click').on('click', function() {
                        $.ajax({
                            url: "<?php echo e(route('attendance-type-setting.destroy', ':id')); ?>".replace(':id', id),
                            type: 'DELETE',
                            data: {
                                _token: "<?php echo e(csrf_token()); ?>"
                            },
                            success: function(response) {
                                $('#confirmationModal').modal('hide');
                                if (response.status) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Berhasil!',
                                        text: response.message,
                                        timer: 2000,
                                        showConfirmButton: false
                                    });
                                    table.ajax.reload();
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Gagal!',
                                        text: response.message
                                    });
                                }
                            },
                            error: function(xhr) {
                                $('#confirmationModal').modal('hide');
                                let response = xhr.responseJSON;
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Terjadi Kesalahan!',
                                    text: response.message || 'Terjadi kesalahan'
                                });
                            }
                        });
                    });
                });
            });

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            // Add search input ID for reference
            $(document).ready(function() {
                $('input[placeholder="Cari nama/kode jenis absen"]').attr('id', 'search-input');
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/attendance-type-setting/index.blade.php ENDPATH**/ ?>