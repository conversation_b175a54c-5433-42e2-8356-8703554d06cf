<nav class="nxl-navigation">
    <div class="navbar-wrapper">
        <div class="m-header justify-content-center">
            <a href="{{ route('dashboard') }}" class="b-brand">
                 <h5 class="fs-16 fw-bolder mb-1 text-center">Sistem Manajemen <PERSON></h5>
            </a>
        </div>
        <div class="navbar-content">
            <ul class="nxl-navbar">
                @php
                    $menus = [
                        [
                            'icon' => 'feather-airplay',
                            'name' => 'Dashboard',
                            'url' => route('dashboard'),
                            'active' => ['dashboard.index'],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-user',
                            'name' => 'Management',
                            'url' => '#',
                            'active' => [
                                'pengguna.index',
                                'pengguna.create',
                                'pengguna.edit',
                                'role-permission.index',
                                'role-permission.create',
                                'role-permission.edit',
                            ],
                            'child' => [
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Pengguna',
                                    'url' => route('pengguna.index'),
                                    'permission' => 'Pengguna.List',
                                    'active' => ['pengguna.index', 'pengguna.create', 'pengguna.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Peran & Hak Akses',
                                    'permission' => 'Role & Permission.List',
                                    'url' => route('role-permission.index'),
                                    'active' => [
                                        'role-permission.index',
                                        'role-permission.create',
                                        'role-permission.edit',
                                    ],
                                    'permission' => 'Role & Permission.List',
                                    'child' => [],
                                ],
                            ],
                        ],
                        [
                            'icon' => 'feather-database',
                            'name' => 'Master Data HRIS',
                            'url' => '#',
                            'active' => [
                                'branch.index', 'branch.create', 'branch.edit',
                                'leave-off-setting.index', 'leave-off-setting.create', 'leave-off-setting.edit',
                                'working-hour-setting.index', 'working-hour-setting.create', 'working-hour-setting.edit',
                                'attendance-type-setting.index', 'attendance-type-setting.create', 'attendance-type-setting.edit',
                                'working-calendar-setting.index', 'working-calendar-setting.create', 'working-calendar-setting.edit',
                                'user-access-branch.index'
                            ],
                            'child' => [
                                [
                                    'icon' => 'feather-map-pin',
                                    'name' => 'Cabang',
                                    'url' => route('branch.index'),
                                    'permission' => 'Master Cabang.List',
                                    'active' => ['branch.index', 'branch.create', 'branch.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-calendar',
                                    'name' => 'Pengaturan Cuti',
                                    'url' => route('leave-off-setting.index'),
                                    'permission' => 'Master Pengaturan Cuti.List',
                                    'active' => ['leave-off-setting.index', 'leave-off-setting.create', 'leave-off-setting.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-clock',
                                    'name' => 'Jam Kerja',
                                    'url' => route('working-hour-setting.index'),
                                    'permission' => 'Master Jam Kerja.List',
                                    'active' => ['working-hour-setting.index', 'working-hour-setting.create', 'working-hour-setting.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-check-circle',
                                    'name' => 'Jenis Absen',
                                    'url' => route('attendance-type-setting.index'),
                                    'permission' => 'Master Jenis Absen.List',
                                    'active' => ['attendance-type-setting.index', 'attendance-type-setting.create', 'attendance-type-setting.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-calendar',
                                    'name' => 'Kalender Kerja',
                                    'url' => route('working-calendar-setting.index'),
                                    'permission' => 'Master Kalender Kerja.List',
                                    'active' => ['working-calendar-setting.index', 'working-calendar-setting.create', 'working-calendar-setting.edit'],
                                    'child' => [],
                                ],

                            ],
                        ],
                        [
                            'icon' => 'feather-settings',
                            'name' => 'Pengaturan',
                            'url' => '#',
                            'active' => ['pengaturan.e-sign.index'],
                            'child' => [
                                [
                                    'icon' => '',
                                    'name' => 'Default',
                                    'url' => route('pengaturan.default.index'),
                                    'permission' => 'Pengaturan.Default',
                                    'active' => ['pengaturan.default.index'],
                                    'child' => [],
                                ],
                            ],
                        ],
                    ];
                @endphp
                <li class="nxl-item nxl-caption">
                    <label>Navigation</label>
                </li>
                @foreach ($menus as $item)
                    @if (count($item['child']) > 0)
                        @if (canPermissionMultiple(array_column($item['child'], 'permission')) ||
                                count(array_column($item['child'], 'permission')) === 0)
                            <li
                                class="nxl-item nxl-hasmenu {{ set_active($item['active'] ?? []) }} {{ set_active($item['active'] ?? [], 'nxl-trigger') }}">
                                <a href="{{ $item['url'] ?? '' }}" class="nxl-link">
                                    <span class="nxl-micon"><i class="{{ $item['icon'] }}"></i></span>
                                    <span class="nxl-mtext">{{ $item['name'] }}</span>
                                    <span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                                </a>
                                <ul class="nxl-submenu">
                                    @foreach ($item['child'] as $child)
                                        @if ((isset($child['permission']) && canPermission($child['permission'])) || !isset($child['permission']))
                                            <li class="nxl-item {{ set_active($child['active'] ?? []) }}"><a
                                                    class="nxl-link"
                                                    href="{{ $child['url'] ?? '' }}">{{ $child['name'] }}</a>
                                            </li>
                                        @endif
                                    @endforeach
                                </ul>
                            </li>
                        @endif
                    @else
                        @if ((isset($item['permission']) && canPermission($item['permission'])) || !isset($item['permission']))
                            <li class="nxl-item {{ set_active($item['active'] ?? []) }}">
                                <a href="{{ $item['url'] }}" class="nxl-link">
                                    <span class="nxl-micon"><i class="{{ $item['icon'] }}"></i></span>
                                    <span class="nxl-mtext">{{ $item['name'] }}</span>
                                </a>
                            </li>
                        @endif
                    @endif
                @endforeach
            </ul>
        </div>
    </div>
</nav>
