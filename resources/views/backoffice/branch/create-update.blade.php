<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} Cabang
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('branch.index') }}" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="{{ route('branch.store') }}" method="POST">
                        @csrf
                        @if(isset($data))
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" 
                                           value="{{ old('name', $data->name ?? '') }}" 
                                           placeholder="Masukkan nama cabang" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Kode Cabang <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                           id="code" name="code" 
                                           value="{{ old('code', $data->code ?? '') }}" 
                                           placeholder="Masukkan kode cabang" required>
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select select2 @error('status') is-invalid @enderror"
                                            id="status" name="status" required>
                                        <option value="">Pilih Status</option>
                                        <option value="active" {{ old('status', $data->status ?? '') == 'active' ? 'selected' : '' }}>
                                            Aktif
                                        </option>
                                        <option value="inactive" {{ old('status', $data->status ?? '') == 'inactive' ? 'selected' : '' }}>
                                            Tidak Aktif
                                        </option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('branch.index') }}" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                {{ isset($data) ? 'Update' : 'Simpan' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Nama Cabang:</strong> Nama lengkap cabang/kantor</li>
                            <li><strong>Kode Cabang:</strong> Kode unik untuk identifikasi cabang</li>
                            <li><strong>Status:</strong> Status aktif/tidak aktif cabang</li>
                        </ul>
                    </div>
                    
                    @if(isset($data))
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> {{ $data->created_at->translatedFormat('d F Y H:i') }}</li>
                            @if($data->updated_at != $data->created_at)
                            <li><strong>Diupdate:</strong> {{ $data->updated_at->translatedFormat('d F Y H:i') }}</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });

                // Auto generate code from name
                $('#name').on('input', function() {
                    if (!$('#code').val()) {
                        let name = $(this).val();
                        let code = name.toUpperCase()
                                      .replace(/[^A-Z0-9\s]/g, '')
                                      .replace(/\s+/g, '')
                                      .substring(0, 10);
                        $('#code').val(code);
                    }
                });

                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });
                    
                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });
            });
        </script>
    @endpush
</x-app-layout>
