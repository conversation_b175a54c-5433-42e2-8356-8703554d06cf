<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} <PERSON><PERSON>
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('attendance-type-setting.index') }}" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="{{ route('attendance-type-setting.store') }}" method="POST">
                        @csrf
                        @if(isset($data))
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name"
                                           value="{{ old('name', $data->name ?? '') }}"
                                           placeholder="Masukkan nama jenis absen" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            @if(isset($data))
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Kode</label>
                                    <input type="text" class="form-control"
                                           id="code" name="code"
                                           value="{{ $data->code }}"
                                           readonly>
                                    <small class="text-muted">Kode otomatis dibuat dari nama jenis absen</small>
                                </div>
                            </div>
                            @endif
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reduction_days_leave" class="form-label">Pengurangan Hari Cuti <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('reduction_days_leave') is-invalid @enderror" 
                                           id="reduction_days_leave" name="reduction_days_leave" 
                                           value="{{ old('reduction_days_leave', $data->reduction_days_leave ?? 0) }}" 
                                           placeholder="0" min="0" required>
                                    @error('reduction_days_leave')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Jumlah hari yang dikurangi dari jatah cuti</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reduction_days_off" class="form-label">Pengurangan Hari Off <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('reduction_days_off') is-invalid @enderror" 
                                           id="reduction_days_off" name="reduction_days_off" 
                                           value="{{ old('reduction_days_off', $data->reduction_days_off ?? 0) }}" 
                                           placeholder="0" min="0" required>
                                    @error('reduction_days_off')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Jumlah hari yang dikurangi dari jatah libur</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="additional_days" class="form-label">Tambahan Jika di Weekend/Hari Besar <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('additional_days') is-invalid @enderror" 
                                           id="additional_days" name="additional_days" 
                                           value="{{ old('additional_days', $data->additional_days ?? 0) }}" 
                                           placeholder="0" min="0" required>
                                    @error('additional_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Jumlah hari tambahan yang diberikan</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('attendance-type-setting.index') }}" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                {{ isset($data) ? 'Update' : 'Simpan' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Nama:</strong> Nama jenis absen (contoh: Sakit, Alpha, Izin)</li>
                            <li><strong>Kode:</strong> Kode unik untuk identifikasi</li>
                            <li><strong>Pengurangan Hari Cuti:</strong> Berapa hari cuti yang dikurangi</li>
                            <li><strong>Pengurangan Hari Off:</strong> Berapa hari libur yang dikurangi</li>
                            <li><strong>Tambahan Jika di Weekend/Hari Besar:</strong> Berapa hari tambahan yang diberikan</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Contoh Pengaturan:</h6>
                        <ul class="mb-0">
                            <li><strong>Sakit:</strong> Mengurangi 1 hari cuti, 0 hari libur</li>
                            <li><strong>Alpha:</strong> Mengurangi 1 hari cuti, 1 hari libur</li>
                            <li><strong>Izin:</strong> Mengurangi 1 hari cuti, 0 hari libur</li>
                            <li><strong>Lembur:</strong> Menambah 1 hari libur</li>
                        </ul>
                    </div>
                    
                    @if(isset($data))
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> {{ $data->created_at->translatedFormat('d F Y H:i') }}</li>
                            @if($data->updated_at != $data->created_at)
                            <li><strong>Diupdate:</strong> {{ $data->updated_at->translatedFormat('d F Y H:i') }}</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Auto generate code from name
                $('#name').on('input', function() {
                    if (!$('#code').val()) {
                        let name = $(this).val();
                        let code = name.toUpperCase()
                                      .replace(/[^A-Z0-9\s]/g, '')
                                      .replace(/\s+/g, '_')
                                      .substring(0, 10);
                        $('#code').val(code);
                    }
                });

                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });
                    
                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });

                // Clear validation on input change
                $('input, select').on('change', function() {
                    $(this).removeClass('is-invalid');
                });
            });
        </script>
    @endpush
</x-app-layout>
