<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} Pengaturan Cuti
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('leave-off-setting.index') }}" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="{{ route('leave-off-setting.store') }}" method="POST">
                        @csrf
                        @if(isset($data))
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Pengaturan Cuti <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name"
                                           value="{{ old('name', $data->name ?? '') }}"
                                           placeholder="Masukkan nama pengaturan cuti" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            @if(isset($data))
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Kode</label>
                                    <input type="text" class="form-control"
                                           id="code" name="code"
                                           value="{{ $data->code }}"
                                           readonly>
                                    <small class="text-muted">Kode otomatis dibuat dari nama pengaturan cuti</small>
                                </div>
                            </div>
                            @endif
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="leave_type" class="form-label">Jenis Cuti <span class="text-danger">*</span></label>
                                    <select class="form-select select2 @error('leave_type') is-invalid @enderror"
                                            id="leave_type" name="leave_type" required>
                                        <option value="">Pilih Jenis Cuti</option>
                                        @foreach($leaveTypes as $key => $value)
                                            <option value="{{ $key }}" {{ old('leave_type', $data->leave_type ?? '') == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('leave_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_leave_days" class="form-label">Jatah Hari Cuti <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('max_leave_days') is-invalid @enderror" 
                                           id="max_leave_days" name="max_leave_days" 
                                           value="{{ old('max_leave_days', $data->max_leave_days ?? '') }}" 
                                           placeholder="Masukkan maksimal hari cuti" min="0" required>
                                    @error('max_leave_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                               <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_leave_days" class="form-label">Masa Kerja Minimal (Bulan) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('max_leave_days') is-invalid @enderror" 
                                           id="min_service_length" name="min_service_length" 
                                           value="{{ old('min_service_length', $data->min_service_length ?? '') }}" 
                                           placeholder="Masukkan masa kerja minimal" min="0" required>
                                    @error('min_service_length')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="service_length_formula" class="form-label">Formula Masa Kerja</label>
                                    <textarea class="form-control @error('service_length_formula') is-invalid @enderror" 
                                              id="service_length_formula" name="service_length_formula" 
                                              rows="3" placeholder="Masukkan formula perhitungan berdasarkan masa kerja (opsional)">{{ old('service_length_formula', $data->service_length_formula ?? '') }}</textarea>
                                    @error('service_length_formula')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Contoh: 0-6 bulan = 0 hari, 1 tahun = 12 hari, 2 tahun = 15 hari
                                    </div>
                                </div>
                            </div>
                        </div> --}}

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('leave-off-setting.index') }}" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                {{ isset($data) ? 'Update' : 'Simpan' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Nama:</strong> Nama jenis cuti (contoh: Cuti Tahunan, Cuti Sakit)</li>
                            <li><strong>Kode:</strong> Kode unik untuk identifikasi</li>
                            <li><strong>Jenis Cuti:</strong> Apakah memotong jatah cuti atau tidak</li>
                            <li><strong>Jatah:</strong> Jatah Cuti hari cuti per periode</li>
                            <li><strong>Masa Kerja Minimal:</strong> Masa kerja minimal untuk mendapatkan jatah cuti</li>
                        </ul>
                    </div>
                    
                    @if(isset($data))
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> {{ $data->created_at->translatedFormat('d F Y H:i') }}</li>
                            @if($data->updated_at != $data->created_at)
                            <li><strong>Diupdate:</strong> {{ $data->updated_at->translatedFormat('d F Y H:i') }}</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {


                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });
                    
                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });
            });
        </script>
    @endpush
</x-app-layout>
