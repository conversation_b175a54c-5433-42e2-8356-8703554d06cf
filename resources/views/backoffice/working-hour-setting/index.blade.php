<x-app-layout>
    <x-slot name="header">
        Master Data Pengaturan Jam Kerja
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" id="search-input"
                                placeholder="Cari role/jenis jam kerja" oninput="handleSearch(event)"
                                style="width: 100%" />
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            {{-- @if (canPermission('Working Hour Setting.Create')) --}}
                            <div>
                                <a href="{{ route('working-hour-setting.create') }}" class="btn btn-primary">
                                    <i class="feather-plus me-2"></i>
                                    <span>Tambah Pengaturan Jam Kerja</span>
                                </a>
                            </div>
                            {{-- @endif --}}
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Pengaturan Jam Kerja:</h6>
                        <ul class="mb-0">
                            <li><strong>Shift Pagi:</strong> Jam kerja pagi hari</li>
                            <li><strong>Shift Siang:</strong> Jam kerja siang hari</li>
                            <li><strong>Shift Malam:</strong> Jam kerja malam hari</li>
                            <li><strong>Full Day:</strong> Jam kerja penuh dalam sehari</li>
                            <li><strong>Perlu Pengaturan Hari:</strong> Apakah perlu pengaturan hari kerja khusus</li>
                        </ul>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Role</th>
                                    <th>Jenis Jam Kerja</th>
                                    <th>Jam Kerja</th>
                                    <th>Perlu Pengaturan Hari</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('components.alert-confirmation')

    @push('scripts')
        @include('libs.datatable')
        <script>
            let table;
            let searchTimeout;

            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('working-hour-setting.dataTable') }}",
                        type: "POST",
                        data: function(d) {
                            d._token = "{{ csrf_token() }}";
                            d.keyword = $('#search-input').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'role_name',
                            name: 'role.name'
                        },
                        {
                            data: 'working_hour_type_badge',
                            name: 'working_hour_type',
                            orderable: false
                        },
                        {
                            data: 'time_range',
                            name: 'start_time',
                            orderable: false
                        },
                        {
                            data: 'need_setting_day_badge',
                            name: 'is_need_setting_day',
                            orderable: false
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ]
                });

                $(document).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = `Jam Kerja ${dataInput.role?.name || 'Role'}`;
                    const urlTarget = `${base_url}/working-hour-setting/${id}`
                    await deleteDataTable(nama, urlTarget, table)
                });
            });

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            // Add search input ID for reference
            $(document).ready(function() {
                $('input[placeholder="Cari role/jenis jam kerja"]').attr('id', 'search-input');
            });
        </script>
    @endpush
</x-app-layout>
