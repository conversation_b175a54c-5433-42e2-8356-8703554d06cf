<?php

namespace App\Http\Controllers;

use App\Models\LeaveOffSetting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class LeaveOffSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.leave-off-setting.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $leaveTypes = LeaveOffSetting::getLeaveTypes();
        return view('backoffice.leave-off-setting.create-update', compact('leaveTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('leave_off_settings', 'name')->ignore($id)->whereNull('deleted_at'),
            ],
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('leave_off_settings', 'code')->ignore($id)->whereNull('deleted_at'),
            ],
            'leave_type' => 'required|in:cutting,non-cutting',
            'service_length_formula' => 'nullable|string',
            'max_leave_days' => 'required|integer|min:0',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = [
                'name' => $request->name,
                'code' => $request->code,
                'leave_type' => $request->leave_type,
                'service_length_formula' => $request->service_length_formula,
                'max_leave_days' => $request->max_leave_days,
            ];

            LeaveOffSetting::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('leave-off-setting.index'))->with('success', 'Pengaturan cuti berhasil disimpan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = LeaveOffSetting::findOrFail($id);
        return view('backoffice.leave-off-setting.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = LeaveOffSetting::findOrFail($id);
        $leaveTypes = LeaveOffSetting::getLeaveTypes();
        return view('backoffice.leave-off-setting.create-update', compact('data', 'leaveTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $leaveOffSetting = LeaveOffSetting::findOrFail($id);

            DB::transaction(function() use ($leaveOffSetting) {
                $leaveOffSetting->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = LeaveOffSetting::select(
            'id',
            'name',
            'code',
            'leave_type',
            'max_leave_days',
            'created_at'
        )
        ->latest()
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('leave_type_badge', function ($data) {
                $badgeClass = $data->leave_type === 'cutting' ? 'badge-soft-danger' : 'badge-soft-success';
                return "<span class='badge {$badgeClass}'>{$data->leave_type_name}</span>";
            })
            ->addColumn('max_leave_days_formatted', function ($data) {
                return $data->max_leave_days . ' hari';
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                if(canPermission('Leave Off Setting.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('leave-off-setting.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Leave Off Setting.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'leave_type_badge'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get list of leave off settings for select options
     */
    public function list(Request $request)
    {
        $settings = LeaveOffSetting::select('id', 'name', 'code', 'leave_type', 'max_leave_days')
                                  ->when($request->keyword, function($q) use ($request) {
                                      $q->where('name', 'like', '%'.$request->keyword.'%')
                                        ->orWhere('code', 'like', '%'.$request->keyword.'%');
                                  })
                                  ->when($request->leave_type, function($q) use ($request) {
                                      $q->where('leave_type', $request->leave_type);
                                  })
                                  ->orderBy('name')
                                  ->get();
        
        return response()->json($settings);
    }
}
