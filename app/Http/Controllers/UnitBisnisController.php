<?php

namespace App\Http\Controllers;

use App\Models\UnitBisnis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\Controller;

class UnitBisnisController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.unit-bisnis.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->unit_bisnis_id;
        $request->validate([
            'nama' => $request->unit_bisnis_id ? 'required|unique:unit_bisnis,nama,'.$request->unit_bisnis_id : 'required|unique:unit_bisnis,nama',
            'setting_no_surat' => 'required',
        ]);

        $input = $request->all();

        try {
            DB::transaction(function() use ($input, $id, $request){
                $input['is_holding'] = $request->is_holding === "true" ? 1 : 0;
                $input['created_by_id'] = Auth::user()->id;

                // Auto-generate kode_unit_surat if creating new record
                if (!$id) {
                    $input['kode_unit_surat'] = $this->generateUnitBisnisCode($request->nama);
                }

                UnitBisnis::updateOrCreate(['id' => $id], $input);
            });

            return response()->json(['status' => true,'message' => 'Data berhasil disimpan'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Generate unique unit bisnis code from name
     */
    private function generateUnitBisnisCode($name)
    {
        // Create base code from name
        $baseCode = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));
        $baseCode = substr($baseCode, 0, 6); // Limit to 6 characters for unit code

        // If base code is empty, use default
        if (empty($baseCode)) {
            $baseCode = 'UNIT';
        }

        $code = $baseCode;
        $counter = 1;

        // Check for uniqueness and add counter if needed
        while (UnitBisnis::where('kode_unit_surat', $code)->whereNull('deleted_at')->exists()) {
            $code = $baseCode . str_pad($counter, 2, '0', \STR_PAD_LEFT);
            $counter++;
        }

        return $code;
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $unit_bisnis = UnitBisnis::findOrFail($id);
            DB::transaction(function() use ($unit_bisnis){
                $unit_bisnis->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = UnitBisnis::select(
            'id',
            'nama',
            'keterangan',
            'kode_unit_surat',
            'setting_no_surat',
            'start_no_urut',
            'is_holding',
        )
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {
                $action_button = "";
                if(canPermission('Unit Bisnis.Edit')){
                    $action_button .= " <li>
                                    <a class='dropdown-item editInput' href='javascript:void(0)' data-id='$data->id' data-input='".json_encode($data)."'>
                                        <i class='feather feather-edit-3 me-3'></i>
                                        <span>Edit</span>
                                    </a>
                                </li>";
                }

                if(canPermission('Unit Bisnis.Delete')){
                    $action_button .= "
                                <li class='dropdown-divider'></li>
                                <li>
                                    <a class='dropdown-item deleteData' href='javascript:void(0)' data-id='$data->id' data-input='".json_encode($data)."'>
                                        <i class='feather feather-trash-2 me-3'></i>
                                        <span>Delete</span>
                                    </a>
                                </li>";
                }


                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action'])
            ->smart(true)
            ->make(true);
    }


    public function changeSession(Request $request)
    {
        $unit_bisnis_id = $request->unit_bisnis_id;
        if($unit_bisnis_id){
            $unit_bisnis = UnitBisnis::findOrFail($unit_bisnis_id);

            session(['unit_bisnis_id' => $unit_bisnis_id, 'unit_bisnis' => $unit_bisnis]);
        }
        return back();
    }
}
