<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class BranchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.branch.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.branch.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('branches', 'name')->ignore($id)->whereNull('deleted_at'),
            ],
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('branches', 'code')->ignore($id)->whereNull('deleted_at'),
            ],
            'status' => 'required|in:active,inactive',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = [
                'name' => $request->name,
                'code' => $request->code,
                'status' => $request->status,
            ];

            Branch::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('branch.index'))->with('success', 'Cabang berhasil disimpan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = Branch::findOrFail($id);
        return view('backoffice.branch.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = Branch::findOrFail($id);
        return view('backoffice.branch.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $branch = Branch::findOrFail($id);

            // Check if branch has users
            if ($branch->userAccessBranches()->count() > 0) {
                return response()->json([
                    'status' => false,
                    'message' => 'Cabang tidak dapat dihapus karena masih memiliki pengguna yang terkait'
                ], 400);
            }

            DB::transaction(function() use ($branch) {
                $branch->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Branch::select(
            'id',
            'name',
            'code',
            'status',
            'created_at'
        )
        ->latest()
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('status_badge', function ($data) {
                $badgeClass = $data->status === 'active' ? 'badge-soft-success' : 'badge-soft-danger';
                $statusText = $data->status === 'active' ? 'Aktif' : 'Tidak Aktif';
                return "<span class='badge {$badgeClass}'>{$statusText}</span>";
            })
            ->addColumn('action', function ($data) {
                $action_button = '';

                if(canPermission('Master Cabang.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('branch.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Master Cabang.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'status_badge'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get list of active branches for select options
     */
    public function list(Request $request)
    {
        $branches = Branch::select('id', 'name', 'code')
                          ->active()
                          ->when($request->keyword, function($q) use ($request) {
                              $q->where('name', 'like', '%'.$request->keyword.'%')
                                ->orWhere('code', 'like', '%'.$request->keyword.'%');
                          })
                          ->orderBy('name')
                          ->get();
        
        return response()->json($branches);
    }
}
