<?php

namespace App\Http\Controllers;

use App\Models\AttendanceTypeSetting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class AttendanceTypeSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.attendance-type-setting.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.attendance-type-setting.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('attendance_type_settings', 'name')->ignore($id)->whereNull('deleted_at'),
            ],
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('attendance_type_settings', 'code')->ignore($id)->whereNull('deleted_at'),
            ],
            'reduction_days_leave' => 'required|integer|min:0',
            'reduction_days_off' => 'required|integer|min:0',
            'additional_days' => 'required|integer|min:0',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = [
                'name' => $request->name,
                'code' => $request->code,
                'reduction_days_leave' => $request->reduction_days_leave,
                'reduction_days_off' => $request->reduction_days_off,
                'additional_days' => $request->additional_days,
            ];

            AttendanceTypeSetting::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('attendance-type-setting.index'))->with('success', 'Pengaturan jenis absen berhasil disimpan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = AttendanceTypeSetting::findOrFail($id);
        return view('backoffice.attendance-type-setting.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = AttendanceTypeSetting::findOrFail($id);
        return view('backoffice.attendance-type-setting.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $attendanceTypeSetting = AttendanceTypeSetting::findOrFail($id);

            DB::transaction(function() use ($attendanceTypeSetting) {
                $attendanceTypeSetting->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = AttendanceTypeSetting::select(
            'id',
            'name',
            'code',
            'reduction_days_leave',
            'reduction_days_off',
            'additional_days',
            'created_at'
        )
        ->latest()
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('reduction_leave_formatted', function ($data) {
                return $data->reduction_days_leave . ' hari';
            })
            ->addColumn('reduction_off_formatted', function ($data) {
                return $data->reduction_days_off . ' hari';
            })
            ->addColumn('additional_formatted', function ($data) {
                return $data->additional_days . ' hari';
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                if(canPermission('Attendance Type Setting.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('attendance-type-setting.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Attendance Type Setting.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get list of attendance type settings for select options
     */
    public function list(Request $request)
    {
        $settings = AttendanceTypeSetting::select('id', 'name', 'code', 'reduction_days_leave', 'reduction_days_off', 'additional_days')
                                        ->when($request->keyword, function($q) use ($request) {
                                            $q->where('name', 'like', '%'.$request->keyword.'%')
                                              ->orWhere('code', 'like', '%'.$request->keyword.'%');
                                        })
                                        ->orderBy('name')
                                        ->get();
        
        return response()->json($settings);
    }
}
